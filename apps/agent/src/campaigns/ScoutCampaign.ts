import { writeFileSync, readFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { z } from 'zod';
import { <PERSON><PERSON>, WatchEvent } from '@mastra/core';
import { input } from '@inquirer/prompts';
// import { creatorScoutWorkflow } from '@/workflows/creatorScoutWorkflow'; // Not directly used, workflow is accessed via mastra
import { saveCreatorResultsToFile } from '@/utils/saveResults';

// Campaign configuration schema
export const CampaignConfigSchema = z.object({
  // Campaign identification
  campaignId: z.string(),
  campaignName: z.string(),
  description: z.string().optional(),

  // Campaign-level targets
  targetKOLCount: z.number().min(1).default(500), // Total KOLs needed across all workflows
  kolPerTask: z.number().min(1).default(100), // KOLs per individual workflow run
  maxWorkflowRuns: z.number().min(1).default(10), // Maximum workflow runs to prevent infinite loops

  // Shared workflow configuration
  sharedConfig: z.object({
    targetCreatorDescription: z.string(),
    useIntelligentChallengeSelection: z.boolean().default(true),
    filterMode: z.enum(['STRICT', 'LOOSE']).default('LOOSE'),
    pickerMode: z
      .enum(['STRATEGIC', 'OPPORTUNITY', 'VOLUME'])
      .default('STRATEGIC'),
    minViews: z.number().default(0),
    minLikes: z.number().default(0),
    minComments: z.number().default(0),
    minFollowers: z.number().default(0),
    minRecentMedianViews: z.number().default(0),
    minRecentMedianComments: z.number().default(0),
    minRecentMedianLikes: z.number().default(0),
  }),

  // Campaign execution settings
  concurrentTasksLimit: z.number().min(1).max(10).default(4), // Respect rate limits
  persistenceType: z.enum(['json', 'sqlite']).default('json'),
  outputDirectory: z.string().default('./campaign-results'),

  // Progressive reporting settings
  enableProgressiveReporting: z.boolean().default(true),
  reportingInterval: z.number().min(1).default(1), // Report every N workflow runs
});

export type CampaignConfig = z.infer<typeof CampaignConfigSchema>;

// Campaign state schema
export const CampaignStateSchema = z.object({
  campaignId: z.string(),
  status: z
    .enum(['running', 'paused', 'completed', 'failed'])
    .default('running'),
  startTime: z.string(), // ISO timestamp
  lastUpdateTime: z.string(), // ISO timestamp

  // Progress tracking
  workflowRunsCompleted: z.number().default(0),
  totalUniqueKOLs: z.number().default(0),
  totalScoutedResults: z.number().default(0),

  // Deduplication tracking
  scoutedVideoIds: z.array(z.string()).default([]),
  scoutedCreatorIds: z.array(z.string()).default([]),

  // Results aggregation
  allResults: z.array(z.any()).default([]),

  // Statistics
  statistics: z
    .object({
      averageKOLsPerRun: z.number().default(0),
      totalExecutionTime: z.number().default(0), // milliseconds
      lastRunDuration: z.number().default(0), // milliseconds
      successfulRuns: z.number().default(0),
      failedRuns: z.number().default(0),
    })
    .default({}),
});

export type CampaignState = z.infer<typeof CampaignStateSchema>;

// Campaign result schema
export const CampaignResultSchema = z.object({
  batchNumber: z.number(),
  newUniqueKOLs: z.number(),
  totalUniqueKOLs: z.number(),
  executionTime: z.number(), // milliseconds
  timestamp: z.string(), // ISO timestamp
  results: z.array(z.any()),
});

export type CampaignResult = z.infer<typeof CampaignResultSchema>;

/**
 * Scout Campaign class for managing multi-workflow KOL scouting campaigns
 */
export class ScoutCampaign {
  private config: CampaignConfig;
  private state: CampaignState;
  private mastra: Mastra;
  private campaignStartTime: number = 0;

  constructor(config: CampaignConfig, mastra: Mastra) {
    this.config = CampaignConfigSchema.parse(config);
    this.mastra = mastra;

    // Initialize or load campaign state
    this.state = this.loadCampaignState();

    // Ensure output directory exists
    this.ensureOutputDirectory();
  }

  /**
   * Start or resume the scout campaign
   */
  async runCampaign(): Promise<CampaignResult[]> {
    this.campaignStartTime = Date.now();
    this.state.status = 'running';
    this.state.startTime = new Date().toISOString();

    console.log(this.createCampaignSeparator('CAMPAIGN START', 'START'));
    console.log(`🎯 Campaign: ${this.config.campaignName}`);
    console.log(
      `📋 Description: ${this.config.description || 'No description'}`,
    );
    console.log(`🎯 Target KOLs: ${this.config.targetKOLCount}`);
    console.log(`📊 KOLs per task: ${this.config.kolPerTask}`);
    console.log(`🔄 Max workflow runs: ${this.config.maxWorkflowRuns}`);
    console.log(`💾 Persistence: ${this.config.persistenceType}`);
    console.log(`📁 Output directory: ${this.config.outputDirectory}`);

    const campaignResults: CampaignResult[] = [];

    try {
      while (this.shouldContinueCampaign()) {
        const batchNumber = this.state.workflowRunsCompleted + 1;

        console.log(this.createBatchSeparator(batchNumber, 'START'));
        console.log(
          `🚀 Starting workflow run ${batchNumber}/${this.config.maxWorkflowRuns}`,
        );
        console.log(
          `📊 Current unique KOLs: ${this.state.totalUniqueKOLs}/${this.config.targetKOLCount}`,
        );

        const batchStartTime = Date.now();

        try {
          // Run single workflow with deduplication
          const batchResult = await this.runSingleWorkflow(batchNumber);

          const batchDuration = Date.now() - batchStartTime;
          batchResult.executionTime = batchDuration;

          // Update campaign state
          this.updateCampaignState(batchResult, batchDuration);

          // Save results
          await this.saveBatchResults(batchResult);

          campaignResults.push(batchResult);

          // Progressive reporting
          if (
            this.config.enableProgressiveReporting &&
            batchNumber % this.config.reportingInterval === 0
          ) {
            this.generateProgressReport(batchResult);
          }

          console.log(
            this.createBatchSeparator(batchNumber, 'END', batchDuration),
          );
        } catch (error) {
          console.error(`❌ Workflow run ${batchNumber} failed:`, error);
          this.state.statistics.failedRuns++;

          // Continue with next run unless it's a critical error
          if (this.isCriticalError(error)) {
            throw error;
          }
        }

        // Save campaign state after each run
        this.saveCampaignState();
      }

      // Campaign completed
      this.state.status = 'completed';
      const totalDuration = Date.now() - this.campaignStartTime;
      this.state.statistics.totalExecutionTime = totalDuration;

      console.log(
        this.createCampaignSeparator(
          'CAMPAIGN COMPLETED',
          'END',
          totalDuration,
        ),
      );
      this.generateFinalReport();
    } catch (error) {
      this.state.status = 'failed';
      console.error('❌ Campaign failed:', error);
      throw error;
    } finally {
      this.saveCampaignState();
    }

    return campaignResults;
  }

  /**
   * Run a single workflow with the current campaign configuration
   * Enhanced for campaign-specific features like sequential cursor processing
   */
  private async runSingleWorkflow(
    batchNumber: number,
  ): Promise<CampaignResult> {
    const registeredWorkflow = this.mastra.getWorkflow('creatorScoutWorkflow');
    const run = registeredWorkflow.createRun();

    // Prepare input data with current campaign settings
    // For campaigns, we want sequential processing (cursor starts from 0)
    const inputData = {
      ...this.config.sharedConfig,
      desiredCreatorCount: this.config.kolPerTask,
      // Campaign-specific overrides for deep deduplication
      campaignMode: true, // Flag to indicate this is part of a campaign
      batchNumber: batchNumber,
      skipVideoIds: [...this.state.scoutedVideoIds], // Pass already scouted video IDs (copy array)
      skipCreatorIds: [...this.state.scoutedCreatorIds], // Pass already scouted creator IDs (copy array)
      sequentialCursorMode: true, // Always start from cursor 0 for campaigns
    };

    console.log(
      `📝 Workflow input configured for campaign batch ${batchNumber}`,
    );
    // console.log(
    //   `🚫 Skipping ${this.state.scoutedVideoIds.length} already scouted videos`,
    // );
    console.log(
      `🚫 Skipping ${this.state.scoutedCreatorIds.length} already scouted creators`,
    );

    // Set up workflow watch for human interaction
    const workflowPromise = new Promise<any>((resolve, reject) => {
      run.watch(async (watchEvents: WatchEvent) => {
        if (watchEvents.payload.workflowState.status !== 'running') {
          return;
        }

        const steps = Object.entries(watchEvents.payload.workflowState.steps);

        for (const [stepId, step] of steps) {
          if (step && step.status === 'suspended') {
            const { message, messages } = step.payload!;

            // Enhanced logging for campaign context
            console.log('\n' + '='.repeat(60));
            console.log(`🎯 CAMPAIGN INTERACTION - Batch ${batchNumber}`);
            console.log('='.repeat(60));
            console.log(message.text);
            console.log('='.repeat(60) + '\n');

            const answer = await input({
              message: `[Campaign Batch ${batchNumber}] Please enter your response:`,
            });

            console.log(`📝 User response for batch ${batchNumber}:`, answer);

            await run.resume({
              step: stepId,
              resumeData: {
                messages,
                userInputMessage: answer,
              },
            });
          }
        }
      });

      // Start the workflow
      run.start({ inputData }).then(resolve).catch(reject);
    });

    const result = await workflowPromise;

    // Process and deduplicate results
    return await this.processWorkflowResults(result, batchNumber);
  }

  /**
   * Process workflow results and perform deduplication
   */
  private async processWorkflowResults(
    workflowResult: any,
    batchNumber: number,
  ): Promise<CampaignResult> {
    // Extract results from workflow output
    let newResults: any[] = [];

    // Handle different result formats
    if (workflowResult.result?.contextId) {
      // Results are stored in database - fetch them using contextId
      console.log(
        `📊 Results stored with contextId: ${workflowResult.result.contextId}`,
      );

      try {
        // Import the helper function to get results by contextId
        const { getCreatorScoutResultsByContextId } = await import(
          '@/workflows/creatorScoutWorkflow'
        );
        const dbResults = await getCreatorScoutResultsByContextId(
          workflowResult.result.contextId,
        );

        if (dbResults?.results) {
          newResults = dbResults.results;
          console.log(
            `📊 Retrieved ${newResults.length} results from database`,
          );
        } else if (dbResults) {
          // Check if results are in a different structure
          console.log(`📊 Database results structure:`, Object.keys(dbResults));

          // Try to extract results from different possible locations
          if (Array.isArray(dbResults)) {
            newResults = dbResults;
            console.log(
              `📊 Using dbResults array directly: ${newResults.length} items`,
            );
          } else {
            console.warn(
              `⚠️ Results found but not in expected format for contextId: ${workflowResult.result.contextId}`,
            );
            console.log(
              `📊 Available keys in dbResults:`,
              Object.keys(dbResults),
            );
            newResults = [];
          }
        } else {
          console.warn(
            `⚠️ No results found in database for contextId: ${workflowResult.result.contextId}`,
          );
          newResults = [];
        }
      } catch (error) {
        console.error(`❌ Failed to fetch results from database:`, error);
        newResults = [];
      }
    } else if (workflowResult.result?.results) {
      newResults = workflowResult.result.results;
      console.log(
        `📊 Using direct results from workflow: ${newResults.length} items`,
      );
    } else if (Array.isArray(workflowResult)) {
      newResults = workflowResult;
      console.log(
        `📊 Using workflow result as array: ${newResults.length} items`,
      );
    } else {
      console.warn(`⚠️ Unexpected workflow result format:`, workflowResult);
      console.log(
        `📊 Workflow result keys:`,
        Object.keys(workflowResult || {}),
      );
      if (workflowResult?.result) {
        console.log(
          `📊 Workflow result.result keys:`,
          Object.keys(workflowResult.result),
        );
      }
      newResults = [];
    }

    console.log(
      `📊 Processing ${newResults.length} new results for deduplication`,
    );

    // Deduplicate by creator unique_id and video_id
    const uniqueResults: any[] = [];
    let newUniqueKOLs = 0;

    for (const result of newResults) {
      const creatorId = result.creatorMetrics?.unique_id || result.unique_id;
      const videoIds = this.extractVideoIds(result);

      // Check if creator is already scouted
      if (creatorId && !this.state.scoutedCreatorIds.includes(creatorId)) {
        // Check if any video IDs are already scouted
        const hasNewVideos = videoIds.some(
          (videoId) => !this.state.scoutedVideoIds.includes(videoId),
        );

        if (hasNewVideos || videoIds.length === 0) {
          uniqueResults.push(result);
          newUniqueKOLs++;

          // Track this creator and videos
          this.state.scoutedCreatorIds.push(creatorId);
          this.state.scoutedVideoIds.push(...videoIds);
        }
      }
    }

    console.log(
      `✅ Found ${newUniqueKOLs} new unique KOLs (${newResults.length - newUniqueKOLs} duplicates filtered)`,
    );

    // Add to campaign results
    this.state.allResults.push(...uniqueResults);
    this.state.totalUniqueKOLs += newUniqueKOLs;
    this.state.totalScoutedResults += newResults.length;

    return {
      batchNumber,
      newUniqueKOLs,
      totalUniqueKOLs: this.state.totalUniqueKOLs,
      executionTime: 0, // Will be set by caller
      timestamp: new Date().toISOString(),
      results: uniqueResults,
    };
  }

  /**
   * Extract video IDs from a result object
   */
  private extractVideoIds(result: any): string[] {
    const videoIds: string[] = [];

    // Try different possible locations for video IDs
    if (result.videos && Array.isArray(result.videos)) {
      videoIds.push(
        ...result.videos.map((v: any) => v.video_id).filter(Boolean),
      );
    }

    if (result.video_id) {
      videoIds.push(result.video_id);
    }

    if (result.recentVideos && Array.isArray(result.recentVideos)) {
      videoIds.push(
        ...result.recentVideos.map((v: any) => v.video_id).filter(Boolean),
      );
    }

    return videoIds;
  }

  /**
   * Check if campaign should continue running
   */
  private shouldContinueCampaign(): boolean {
    // Stop if we've reached the target KOL count
    if (this.state.totalUniqueKOLs >= this.config.targetKOLCount) {
      console.log(
        `🎯 Target KOL count reached: ${this.state.totalUniqueKOLs}/${this.config.targetKOLCount}`,
      );
      return false;
    }

    // Stop if we've reached the maximum workflow runs
    if (this.state.workflowRunsCompleted >= this.config.maxWorkflowRuns) {
      console.log(
        `🔄 Maximum workflow runs reached: ${this.state.workflowRunsCompleted}/${this.config.maxWorkflowRuns}`,
      );
      return false;
    }

    // Stop if campaign is not in running status
    if (this.state.status !== 'running') {
      console.log(`⏸️ Campaign status is not running: ${this.state.status}`);
      return false;
    }

    return true;
  }

  /**
   * Update campaign state after a workflow run
   */
  private updateCampaignState(
    _batchResult: CampaignResult,
    duration: number,
  ): void {
    this.state.workflowRunsCompleted++;
    this.state.lastUpdateTime = new Date().toISOString();
    this.state.statistics.lastRunDuration = duration;
    this.state.statistics.successfulRuns++;

    // Calculate average KOLs per run
    if (this.state.workflowRunsCompleted > 0) {
      this.state.statistics.averageKOLsPerRun =
        this.state.totalUniqueKOLs / this.state.workflowRunsCompleted;
    }
  }

  /**
   * Load campaign state from persistence
   */
  private loadCampaignState(): CampaignState {
    const stateFile = this.getStateFilePath();

    if (existsSync(stateFile)) {
      try {
        const stateData = JSON.parse(readFileSync(stateFile, 'utf8'));
        console.log(`📂 Loaded existing campaign state from ${stateFile}`);
        return CampaignStateSchema.parse(stateData);
      } catch (error) {
        console.warn(
          `⚠️ Failed to load campaign state, starting fresh:`,
          error,
        );
      }
    }

    // Create new state
    return {
      campaignId: this.config.campaignId,
      status: 'running',
      startTime: new Date().toISOString(),
      lastUpdateTime: new Date().toISOString(),
      workflowRunsCompleted: 0,
      totalUniqueKOLs: 0,
      totalScoutedResults: 0,
      scoutedVideoIds: [],
      scoutedCreatorIds: [],
      allResults: [],
      statistics: {
        averageKOLsPerRun: 0,
        totalExecutionTime: 0,
        lastRunDuration: 0,
        successfulRuns: 0,
        failedRuns: 0,
      },
    };
  }

  /**
   * Save campaign state to persistence
   */
  private saveCampaignState(): void {
    const stateFile = this.getStateFilePath();

    try {
      writeFileSync(stateFile, JSON.stringify(this.state, null, 2), 'utf8');
      console.log(`💾 Campaign state saved to ${stateFile}`);
    } catch (error) {
      console.error(`❌ Failed to save campaign state:`, error);
    }
  }

  /**
   * Save batch results to file
   */
  private async saveBatchResults(batchResult: CampaignResult): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `batch-${batchResult.batchNumber}-${timestamp}.json`;
    const filepath = join(this.config.outputDirectory, filename);

    try {
      writeFileSync(filepath, JSON.stringify(batchResult, null, 2), 'utf8');
      console.log(
        `💾 Batch ${batchResult.batchNumber} results saved to ${filepath}`,
      );

      // Also save using the existing saveCreatorResultsToFile function
      if (batchResult.results.length > 0) {
        const mockExecuteResult = {
          result: {
            results: batchResult.results,
            scoutedCreators: batchResult.results.length,
            qualifiedCreators: batchResult.results.length,
          },
        };

        const mockInputData = this.config.sharedConfig;

        await saveCreatorResultsToFile(
          mockExecuteResult,
          mockInputData,
          this.config.outputDirectory,
          `campaign-${this.config.campaignId}-batch-${batchResult.batchNumber}`,
        );
      }
    } catch (error) {
      console.error(`❌ Failed to save batch results:`, error);
    }
  }

  /**
   * Generate progressive report
   */
  private generateProgressReport(batchResult: CampaignResult): void {
    const progress =
      (this.state.totalUniqueKOLs / this.config.targetKOLCount) * 100;
    const avgKOLsPerRun = this.state.statistics.averageKOLsPerRun;
    const estimatedRemainingRuns = Math.ceil(
      (this.config.targetKOLCount - this.state.totalUniqueKOLs) /
        Math.max(avgKOLsPerRun, 1),
    );

    console.log(
      `\n📊 CAMPAIGN PROGRESS REPORT - Batch ${batchResult.batchNumber}`,
    );
    console.log(
      `🎯 Progress: ${this.state.totalUniqueKOLs}/${this.config.targetKOLCount} (${progress.toFixed(1)}%)`,
    );
    console.log(`📈 New KOLs this batch: ${batchResult.newUniqueKOLs}`);
    console.log(`📊 Average KOLs per run: ${avgKOLsPerRun.toFixed(1)}`);
    console.log(
      `⏱️ Last run duration: ${this.formatDuration(batchResult.executionTime)}`,
    );
    console.log(`🔄 Estimated remaining runs: ${estimatedRemainingRuns}`);
    console.log(`✅ Successful runs: ${this.state.statistics.successfulRuns}`);
    console.log(`❌ Failed runs: ${this.state.statistics.failedRuns}`);
  }

  /**
   * Generate final campaign report
   */
  private generateFinalReport(): void {
    const totalDuration = this.state.statistics.totalExecutionTime;
    const avgDuration =
      this.state.workflowRunsCompleted > 0
        ? totalDuration / this.state.workflowRunsCompleted
        : 0;

    console.log(`\n🎉 FINAL CAMPAIGN REPORT`);
    console.log(`📋 Campaign: ${this.config.campaignName}`);
    console.log(
      `🎯 Target achieved: ${this.state.totalUniqueKOLs}/${this.config.targetKOLCount}`,
    );
    console.log(`🔄 Total workflow runs: ${this.state.workflowRunsCompleted}`);
    console.log(`📊 Total scouted results: ${this.state.totalScoutedResults}`);
    console.log(
      `📈 Average KOLs per run: ${this.state.statistics.averageKOLsPerRun.toFixed(1)}`,
    );
    console.log(
      `⏱️ Total execution time: ${this.formatDuration(totalDuration)}`,
    );
    console.log(`⏱️ Average run duration: ${this.formatDuration(avgDuration)}`);
    console.log(`✅ Successful runs: ${this.state.statistics.successfulRuns}`);
    console.log(`❌ Failed runs: ${this.state.statistics.failedRuns}`);
    console.log(`📁 Results saved to: ${this.config.outputDirectory}`);
  }

  /**
   * Utility methods
   */
  private getStateFilePath(): string {
    return join(
      this.config.outputDirectory,
      `campaign-${this.config.campaignId}-state.json`,
    );
  }

  private ensureOutputDirectory(): void {
    if (!existsSync(this.config.outputDirectory)) {
      mkdirSync(this.config.outputDirectory, { recursive: true });
      console.log(
        `📁 Created output directory: ${this.config.outputDirectory}`,
      );
    }
  }

  private formatDuration(ms: number): string {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
    return `${(ms / 3600000).toFixed(1)}h`;
  }

  private createCampaignSeparator(
    title: string,
    action: 'START' | 'END',
    duration?: number,
  ): string {
    const separator = '🎯'.repeat(40);
    const timestamp = new Date().toISOString();
    const durationText = duration ? ` (${this.formatDuration(duration)})` : '';
    return `\n${separator}\n🚀 CAMPAIGN ${action}: ${title.toUpperCase()}${durationText}\n⏰ ${timestamp}\n${separator}`;
  }

  private createBatchSeparator(
    batchNumber: number,
    action: 'START' | 'END',
    duration?: number,
  ): string {
    const separator = '📊'.repeat(30);
    const timestamp = new Date().toISOString();
    const durationText = duration ? ` (${this.formatDuration(duration)})` : '';
    return `\n${separator}\n🔄 BATCH ${action}: ${batchNumber}${durationText}\n⏰ ${timestamp}\n${separator}`;
  }

  private isCriticalError(error: any): boolean {
    // Define what constitutes a critical error that should stop the campaign
    const criticalErrors = [
      'ENOTFOUND', // Network issues
      'ECONNREFUSED', // Connection refused
      'Authentication failed', // API key issues
    ];

    const errorMessage = error?.message || String(error);
    return criticalErrors.some((critical) => errorMessage.includes(critical));
  }

  /**
   * Public methods for campaign management
   */

  /**
   * Pause the campaign
   */
  pauseCampaign(): void {
    this.state.status = 'paused';
    this.saveCampaignState();
    console.log('⏸️ Campaign paused');
  }

  /**
   * Resume the campaign
   */
  resumeCampaign(): void {
    this.state.status = 'running';
    this.saveCampaignState();
    console.log('▶️ Campaign resumed');
  }

  /**
   * Get current campaign status
   */
  getCampaignStatus(): CampaignState {
    return { ...this.state };
  }

  /**
   * Get campaign configuration
   */
  getCampaignConfig(): CampaignConfig {
    return { ...this.config };
  }
}
